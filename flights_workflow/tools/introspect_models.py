"""
模型内省工具 - 输出JsonSchema格式的Django模型描述
"""

import os
import django
import json
from typing import Dict, List, Any

# 测试时使用配置Django设置
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

from django.db import models
from django.contrib.gis.db.models.fields import GeometryField
from django.contrib.postgres.fields import ArrayField, JSONField

from flights.models import (
    model_root, who_region, climatic_zone, data_base2, admin_base, 
    world_admin, unit_admin_amap, aviation_airports, aviation_aircraft, aviation_flight
)

# 创建模型名称到模型类的映射
AVAILABLE_MODELS = {
    'model_root': model_root,
    'who_region': who_region,
    'climatic_zone': climatic_zone,
    'data_base2': data_base2,
    'admin_base': admin_base,
    'world_admin': world_admin,
    'unit_admin_amap': unit_admin_amap,
    'aviation_airports': aviation_airports,
    'aviation_aircraft': aviation_aircraft,
    'aviation_flight': aviation_flight,
}

def introspect_models(model_list: List[str]) -> Dict[str, Any]:
    """
    内省指定的Django模型，返回JsonSchema格式的模型描述
    
    Args:
        model_list: 要内省的模型名称列表
        
    Returns:
        Dict: JsonSchema格式的模型描述
    """
    try:                
        schemas = {}
        
        for model_name in model_list:
            if model_name not in AVAILABLE_MODELS:
                continue
                
            model_class = AVAILABLE_MODELS[model_name]
            schemas[model_name] = _model_to_jsonschema(model_class)
            
        return schemas
        
    except Exception as e:
        return {"error": str(e)}


def _model_to_jsonschema(model_class) -> Dict[str, Any]:
    """
    将Django模型转换为JsonSchema格式
    
    Args:
        model_class: Django模型类
        
    Returns:
        Dict: JsonSchema格式的模型描述
    """
    # 获取模型描述信息
    model_description = f"Django模型: {model_class.__name__}"
    if model_class.__doc__:
        # 清理文档字符串，去除多余的空白和换行
        doc_lines = [line.strip() for line in model_class.__doc__.strip().split('\n') if line.strip()]
        if doc_lines:
            model_description = ' '.join(doc_lines)
    
    schema = {
        "type": "object",
        "title": model_class._meta.verbose_name or model_class.__name__,
        "description": model_description,
        "properties": {},
    }
    
    # 分析字段
    for field in model_class._meta.get_fields():
        # 过滤掉系统字段 "sys_"
        if field.name.startswith('sys_'):
            continue
            
        if hasattr(field, 'related_model') and field.related_model:
            # 关系字段
            schema["properties"][field.name] = _relationship_to_jsonschema(field)
        else:
            # 数据字段
            field_schema = _field_to_jsonschema(field)
            schema["properties"][field.name] = field_schema
    
    return schema


def _field_to_jsonschema(field) -> Dict[str, Any]:
    """
    将Django字段转换为JsonSchema属性
    """
    field_schema = {
        "title": getattr(field, 'verbose_name', field.name) or field.name
    }
    
    # 添加描述
    if hasattr(field, 'help_text') and field.help_text:
        field_schema["description"] = field.help_text
    
    # 根据字段类型设置JsonSchema类型
    if isinstance(field, models.CharField):
        field_schema["type"] = "string"
            
    elif isinstance(field, models.TextField):
        field_schema["type"] = "string"
        
    elif isinstance(field, models.IntegerField):
        field_schema["type"] = "integer"
        
    elif isinstance(field, models.FloatField):
        field_schema["type"] = "number"
        
    elif isinstance(field, models.DecimalField):
        field_schema["type"] = "number"
        if hasattr(field, 'max_digits'):
            field_schema["multipleOf"] = 10 ** (-field.decimal_places)
            
    elif isinstance(field, models.BooleanField):
        field_schema["type"] = "boolean"
        
    elif isinstance(field, models.DateTimeField):
        field_schema["type"] = "string"
        field_schema["format"] = "date-time"
        
    elif isinstance(field, models.DateField):
        field_schema["type"] = "string"
        field_schema["format"] = "date"
        
    elif isinstance(field, models.TimeField):
        field_schema["type"] = "string"
        field_schema["format"] = "time"
        
    elif isinstance(field, JSONField):
        field_schema["type"] = "object"
        
    elif isinstance(field, ArrayField):
        field_schema["type"] = "array"
        field_schema["items"] = _get_array_item_type(field.base_field)
        
    elif isinstance(field, GeometryField):
        field_schema["type"] = "object"
        # 如果没有help_text，则使用默认描述
        if "description" not in field_schema:
            field_schema["description"] = f"几何字段 ({field.__class__.__name__})"
        
    else:
        field_schema["type"] = "string"
        field_schema["description"] = f"Django字段类型: {field.__class__.__name__}"
    
    # 处理选择字段
    if hasattr(field, 'choices') and field.choices:
        field_schema["enum"] = [choice[0] for choice in field.choices]
        field_schema["enumNames"] = [choice[1] for choice in field.choices]
    
    return field_schema


def _relationship_to_jsonschema(field) -> Dict[str, Any]:
    """
    将关系字段转换为JsonSchema属性
    """
    related_model = field.related_model.__name__
    
    if field.__class__.__name__ in ['ForeignKey', 'OneToOneField']:
        return {
            "type": "integer",
            "title": getattr(field, 'verbose_name', field.name) or field.name,
            "description": f"关联到 {related_model} 的外键"
        }
    elif field.__class__.__name__ == 'ManyToManyField':
        return {
            "type": "array",
            "items": {"type": "integer"},
            "title": getattr(field, 'verbose_name', field.name) or field.name,
            "description": f"与 {related_model} 的多对多关系"
        }
    else:
        return {
            "type": "string",
            "title": getattr(field, 'verbose_name', field.name) or field.name,
            "description": f"关系字段: {field.__class__.__name__} -> {related_model}"
        }


def _get_array_item_type(base_field) -> Dict[str, Any]:
    """
    获取数组字段的元素类型
    """
    if isinstance(base_field, models.CharField):
        return {"type": "string"}
    elif isinstance(base_field, models.IntegerField):
        return {"type": "integer"}
    elif isinstance(base_field, models.FloatField):
        return {"type": "number"}
    else:
        return {"type": "string"}


def introspect_models_tool(model_names: str) -> str:
    """
    供LangGraph工具调用的模型内省函数
    
    Args:
        model_names: 逗号分隔的模型名称字符串，如 "aviation_airports,aviation_flight"
        
    Returns:
        str: JsonSchema格式的模型结构信息
    """
    model_list = [name.strip() for name in model_names.split(',') if name.strip()]
    
    if not model_list:
        return json.dumps({"error": "请提供要内省的模型名称，多个模型用逗号分隔"}, ensure_ascii=False, indent=2)
    
    schemas = introspect_models(model_list)
    return json.dumps(schemas, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    model_names = "aviation_airports,aviation_flight"
    print(introspect_models_tool(model_names))
