"""
样例数据工具 - 获取Django模型的样例数据并输出标准JSON格式
"""

import os
import django
import json
from typing import Dict, List, Any, Optional
from decimal import Decimal
from datetime import datetime, date, time

# 配置Django设置
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

from django.db import models, connection
from django.core.serializers.json import DjangoJSONEncoder
from django.contrib.gis.geos import GEOSGeometry

from flights.models import (
    model_root, who_region, climatic_zone, data_base2, admin_base, 
    world_admin, unit_admin_amap, aviation_airports, aviation_aircraft, aviation_flight
)

# 创建模型名称到模型类的映射
AVAILABLE_MODELS = {
    'model_root': model_root,
    'who_region': who_region,
    'climatic_zone': climatic_zone,
    'data_base2': data_base2,
    'admin_base': admin_base,
    'world_admin': world_admin,
    'unit_admin_amap': unit_admin_amap,
    'aviation_airports': aviation_airports,
    'aviation_aircraft': aviation_aircraft,
    'aviation_flight': aviation_flight,
}


class CustomJSONEncoder(DjangoJSONEncoder):
    """
    自定义JSON编码器，处理Django特殊字段类型
    """
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime, date, time)):
            return obj.isoformat()
        elif isinstance(obj, GEOSGeometry):
            return {
                'type': 'geometry',
                'geom_type': obj.geom_type,
                'srid': obj.srid,
                'wkt': str(obj),
                'coords': obj.coords if hasattr(obj, 'coords') else None
            }
        # PostgreSQL 数组和 JSON 字段已经可以直接序列化
        return super().default(obj)


def get_sample_data(model_name: str, limit: int = 5, exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """
    获取指定Django模型的样例数据
    
    Args:
        model_name: 模型名称
        limit: 返回记录数量限制，默认5条
        exclude_sys_fields: 是否排除系统字段（以sys_开头的字段），默认True
        
    Returns:
        Dict: 包含样例数据的字典
    """
    try:
        if model_name not in AVAILABLE_MODELS:
            return {
                "error": f"模型 '{model_name}' 不存在",
                "available_models": list(AVAILABLE_MODELS.keys())
            }
            
        model_class = AVAILABLE_MODELS[model_name]
        
        # 获取总记录数
        try:
            total_count = model_class.objects.count()
        except Exception as db_error:
            return {
                "error": f"数据库连接或表访问错误: {str(db_error)}",
                "model_name": model_name,
                "table_name": getattr(model_class._meta, 'db_table', 'unknown'),
                "suggestion": "请检查数据库连接配置和表是否存在"
            }
        
        if total_count == 0:
            return {
                "model_name": model_name,
                "total_count": 0,
                "sample_data": [],
                "message": f"模型 '{model_name}' 中没有数据",
                "fields_info": _get_fields_summary(model_class, exclude_sys_fields)
            }
        
        # 使用Django ORM获取样例数据
        try:
            # 检查是否有几何字段，如果有则延迟加载
            geometry_fields = []
            for field in model_class._meta.get_fields():
                if hasattr(field, '__class__') and 'GeometryField' in field.__class__.__name__:
                    geometry_fields.append(field.name)
            
            if geometry_fields:
                # 延迟加载几何字段以避免查询问题
                sample_instances = list(model_class.objects.defer(*geometry_fields)[:limit])
            else:
                sample_instances = list(model_class.objects.all()[:limit])
        except Exception as query_error:
            return {
                "error": f"查询数据时出错: {str(query_error)}",
                "model_name": model_name,
                "total_count": total_count,
                "suggestion": "可能是字段类型或数据库配置问题"
            }
        
        # 转换为字典列表
        sample_records = []
        for instance in sample_instances:
            try:
                record = _model_instance_to_dict(instance, exclude_sys_fields)
                sample_records.append(record)
            except Exception as convert_error:
                # 如果某条记录转换失败，记录错误但继续处理其他记录
                sample_records.append({
                    "error": f"记录转换失败: {str(convert_error)}",
                    "pk": getattr(instance, 'pk', 'unknown')
                })
        
        return {
            "model_name": model_name,
            "total_count": total_count,
            "sample_count": len(sample_records),
            "sample_data": sample_records,
            "fields_info": _get_fields_summary(model_class, exclude_sys_fields)
        }
        
    except Exception as e:
        return {
            "error": f"获取样例数据时出错: {str(e)}",
            "error_type": type(e).__name__,
            "model_name": model_name if 'model_name' in locals() else 'unknown'
        }


def _model_instance_to_dict(instance, exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """
    将Django模型实例转换为字典
    
    Args:
        instance: Django模型实例
        exclude_sys_fields: 是否排除系统字段
        
    Returns:
        Dict: 实例数据字典
    """
    from django.contrib.gis.db.models.fields import GeometryField
    from django.contrib.postgres.fields import ArrayField, JSONField
    
    data = {}
    
    for field in instance._meta.get_fields():
        # 过滤系统字段
        if exclude_sys_fields and field.name.startswith('sys_'):
            continue

        # 跳过反向关系字段
        if hasattr(field, 'related_model') and field.related_model and not hasattr(field, 'attname'):
            continue

        try:
            if hasattr(field, 'related_model') and field.related_model and hasattr(field, 'attname'):
                # 处理外键字段：只获取ID，不查询关联对象
                fk_id = getattr(instance, field.attname, None)
                data[field.name] = fk_id
            else:
                # 处理普通字段
                value = getattr(instance, field.name)
                
                # 特殊处理几何字段
                if isinstance(field, GeometryField):
                    try:
                        if value is not None:
                            data[field.name] = {
                                'type': 'geometry',
                                'geom_type': value.geom_type,
                                'srid': value.srid,
                                'wkt': str(value),  # WKT格式
                                'coords': value.coords if hasattr(value, 'coords') else None
                            }
                        else:
                            data[field.name] = None
                    except Exception as geom_error:
                        data[field.name] = f"<几何字段处理失败: {str(geom_error)}>"
                # ArrayField 和 JSONField 可以直接序列化
                elif isinstance(field, (ArrayField, JSONField)):
                    data[field.name] = value
                else:
                    data[field.name] = value

        except Exception as e:
            # 如果某个字段获取失败，记录错误但继续处理其他字段
            data[field.name] = f"<获取失败: {str(e)}>"
    
    return data





def _get_fields_summary(model_class, exclude_sys_fields: bool = True) -> Dict[str, str]:
    """
    获取模型字段摘要信息
    
    Args:
        model_class: Django模型类
        exclude_sys_fields: 是否排除系统字段
        
    Returns:
        Dict: 字段名到字段类型的映射
    """
    fields_info = {}
    
    for field in model_class._meta.get_fields():
        if exclude_sys_fields and field.name.startswith('sys_'):
            continue
            
        if hasattr(field, 'related_model') and field.related_model:
            fields_info[field.name] = f"{field.__class__.__name__} -> {field.related_model.__name__}"
        else:
            fields_info[field.name] = field.__class__.__name__
    
    return fields_info


def get_sample_data_tool(model_names: str, limit: int = 5) -> str:
    """
    供LangGraph工具调用的样例数据获取函数
    
    Args:
        model_names: 逗号分隔的模型名称字符串，如 "aviation_airports,aviation_flight"
        limit: 每个模型返回的记录数量限制，默认5条
        
    Returns:
        str: JSON格式的样例数据
    """
    model_list = [name.strip() for name in model_names.split(',') if name.strip()]
    
    if not model_list:
        return json.dumps({
            "error": "请提供要获取样例数据的模型名称，多个模型用逗号分隔",
            "available_models": list(AVAILABLE_MODELS.keys())
        }, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
    
    results = {}
    
    for model_name in model_list:
        results[model_name] = get_sample_data(model_name, limit)
    
    return json.dumps(results, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)


def test_database_connection() -> Dict[str, Any]:
    """
    测试数据库连接
    
    Returns:
        Dict: 连接测试结果
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
        return {
            "status": "success",
            "message": "数据库连接正常",
            "database_name": connection.settings_dict.get('NAME', 'unknown'),
            "database_engine": connection.settings_dict.get('ENGINE', 'unknown')
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"数据库连接失败: {str(e)}",
            "error_type": type(e).__name__
        }


def get_model_statistics(model_names: str) -> str:
    """
    获取模型的统计信息（记录数量等）
    
    Args:
        model_names: 逗号分隔的模型名称字符串
        
    Returns:
        str: JSON格式的统计信息
    """
    model_list = [name.strip() for name in model_names.split(',') if name.strip()]
    
    if not model_list:
        return json.dumps({
            "error": "请提供要统计的模型名称，多个模型用逗号分隔"
        }, ensure_ascii=False, indent=2)
    
    statistics = {}
    
    for model_name in model_list:
        try:
            if model_name not in AVAILABLE_MODELS:
                statistics[model_name] = {"error": f"模型 '{model_name}' 不存在"}
                continue
                
            model_class = AVAILABLE_MODELS[model_name]
            count = model_class.objects.count()
            
            statistics[model_name] = {
                "total_records": count,
                "model_verbose_name": model_class._meta.verbose_name or model_class.__name__,
                "table_name": model_class._meta.db_table
            }
            
        except Exception as e:
            statistics[model_name] = {"error": f"获取统计信息时出错: {str(e)}"}
    
    return json.dumps(statistics, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    # 测试样例数据获取
    print("=== 测试航班数据 ===")
    result = get_sample_data_tool("aviation_flight", limit=5)
    print(result)
    
    print("\n=== 测试机场数据（包含几何字段）===")
    result = get_sample_data_tool("aviation_airports", limit=2)
    print(result)