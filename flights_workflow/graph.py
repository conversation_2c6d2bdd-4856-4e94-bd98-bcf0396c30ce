import os
import json
from datetime import datetime
from typing import Any, Dict, List, Optional, TypedDict, Annotated
from dotenv import load_dotenv

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from google import genai

load_dotenv()

if os.getenv("GOOGLE_API_KEY") is None:
    raise ValueError("GOOGLE_API_KEY is not set")
api_key = os.getenv("GOOGLE_API_KEY")


def get_current_date() -> str:
    """获取当前日期"""
    return datetime.now().strftime('%Y-%m-%d')


# 验证ReAct的基础工具
@tool
def get_current_time() -> str:
    """获取当前时间"""
    current_time = datetime.now()
    return f"当前时间是: {current_time.strftime('%Y-%m-%d %H:%M:%S')}"


@tool
def get_weather(location: str) -> str:
    """获取指定地点的天气信息
    
    Args:
        location: 地点名称，如"北京"、"上海"等
    """
    # 这是一个模拟的天气工具，实际应用中应该调用真实的天气API
    weather_data = {
        "北京": "晴天，温度 15°C，微风",
        "上海": "多云，温度 18°C，东南风",
        "广州": "小雨，温度 22°C，南风",
        "深圳": "阴天，温度 20°C，无风"
    }
    
    return weather_data.get(location, f"抱歉，暂时无法获取{location}的天气信息")


# Gemini 2.5 原生搜索工具
@tool
def gemini_search(query: str) -> str:
    """使用Gemini 2.5和原生Google Search API进行网络搜索
    
    Args:
        query: 搜索查询内容
    """
    
    try:     
        # 创建Gemini客户端
        client = genai.Client(api_key=api_key)
        
        # 构建搜索提示
        search_prompt = f"""请基于当前日期 {get_current_date()} 搜索并提供关于以下主题的最新、准确信息: {query} 请简要回答。"""
        
        # 使用原生Google Search API工具进行搜索
        response = client.models.generate_content(
            model='gemini-2.5-flash',
            contents=search_prompt,
            config={"tools": [{"google_search": {}}],
                    "temperature": 0,
            }
        )
        
        return f"搜索结果:\n{response.text}"
        
    except Exception as e:
        return f"搜索失败: {str(e)}"


# 工具映射关系
AVAILABLE_TOOLS = {
    "get_current_time": get_current_time,
    "get_weather": get_weather,
    "gemini_search": gemini_search,
}


def get_tools_by_names(tool_names: List[str]) -> List:
    """根据工具名称获取工具列表，支持动态工具选择
    
    Args:
        tool_names: 要启用的工具名称列表
        
    Returns:
        工具对象列表
    """
    tools = []
    for name in tool_names:
        if name in AVAILABLE_TOOLS:
            tools.append(AVAILABLE_TOOLS[name])
        else:
            print(f"Warning: Tool '{name}' not found in available tools")
    return tools


def get_all_tools() -> List:
    """获取所有可用工具"""
    return list(AVAILABLE_TOOLS.values())


class ReActConfiguration(TypedDict):
    """ReAct workflow配置"""
    model_name: str
    temperature: Optional[float]
    max_tokens: Optional[int]
    enabled_tools: Optional[List[str]]  # 启用的工具列表
    enable_search: Optional[bool]  # 是否启用Gemini网络搜索


# ReAct Agent State
class AgentState(TypedDict):
    """ReAct代理的状态结构，使用LangGraph的消息状态模式"""
    messages: Annotated[List[BaseMessage], add_messages]


def call_model(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    ReAct代理节点 - 负责推理和工具调用决策
    
    功能：
    1. 使用绑定工具的Gemini LLM进行推理
    2. 决定是否需要工具调用
    3. 返回AI消息（可能包含工具调用）
    
    Args:
        state: AgentState - 包含消息历史的状态
        config: RunnableConfig - 运行时配置
        
    Returns:
        Dict[str, Any] - 包含AI响应消息的状态更新
    """
    configuration = config.get("configurable", {})
    
    # 从配置中获取参数
    model_name = configuration.get("model_name", "gemini-2.0-flash")
    temperature = configuration.get("temperature", 0.7)
    enabled_tools = configuration.get("enabled_tools", [])
    
    # 获取启用的工具
    tools = get_tools_by_names(enabled_tools) if enabled_tools else get_all_tools()
    
    # 初始化Gemini LLM
    llm = ChatGoogleGenerativeAI(
        model=model_name,
        temperature=temperature,
        google_api_key=api_key
    )
    
    # 绑定工具到模型，使LLM能够进行工具调用
    if tools:
        llm = llm.bind_tools(tools)
    
    # 准备消息，确保包含系统提示以指导推理逻辑
    messages = state["messages"]
    if not messages or not isinstance(messages[0], SystemMessage):
        system_prompt = """你是一个智能的ReAct代理，具备推理和行动能力。

你的工作流程：
1. 推理（Reasoning）：分析用户的查询，思考是否需要使用工具来获取信息
2. 行动（Acting）：如果需要，选择合适的工具并调用
3. 观察（Observing）：分析工具返回的结果，决定是否需要进一步行动

可用工具：
- get_current_time: 获取当前时间
- get_weather: 获取天气信息
- gemini_search: 进行网络搜索

请根据用户问题进行推理，如果需要外部信息则调用相应工具，否则直接基于已有知识回答。"""
        messages = [SystemMessage(content=system_prompt)] + messages
    
    # 调用LLM进行推理，返回包含推理过程和可能的工具调用的AI消息
    response = llm.invoke(messages)
    
    return {"messages": [response]}


def should_continue(state: AgentState) -> str:
    """
    条件路由函数 - 决定是否继续工具调用
    
    检查最后一条消息是否包含工具调用，实现路由逻辑：
    - 有工具调用返回"continue"
    - 无工具调用返回"end"
    
    Args:
        state: AgentState - 包含消息历史的状态
        
    Returns:
        str - "continue"表示继续执行工具，"end"表示结束workflow
    """
    messages = state["messages"]
    last_message = messages[-1]
    
    # 检查最后一条消息是否包含工具调用
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "continue"  # 有工具调用，继续执行工具
    
    # 无工具调用，结束workflow
    return "end"


def tool_node(state: AgentState) -> Dict[str, Any]:
    """执行工具调用"""
    outputs = []
    last_message = state["messages"][-1]
    
    # 创建工具名称到工具对象的映射
    tools_by_name = {tool.name: tool for tool in get_all_tools()}
    
    # 执行所有工具调用
    for tool_call in last_message.tool_calls:
        try:
            tool_result = tools_by_name[tool_call["name"]].invoke(tool_call["args"])
            outputs.append(
                ToolMessage(
                    content=json.dumps(tool_result) if not isinstance(tool_result, str) else tool_result,
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
        except Exception as e:
            outputs.append(
                ToolMessage(
                    content=f"工具执行错误: {str(e)}",
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
    
    return {"messages": outputs}


# 构建ReAct工作流
builder = StateGraph(AgentState, config_schema=ReActConfiguration)

# 添加节点
builder.add_node("agent", call_model)
builder.add_node("tools", tool_node)

# 设置入口点
builder.add_edge(START, "agent")

# 添加条件边
builder.add_conditional_edges(
    "agent",
    should_continue,
    {
        "continue": "tools",
        "end": END,
    },
)

# 工具执行后返回到代理
builder.add_edge("tools", "agent")

# 编译图
graph = builder.compile()

